import { RequestType } from "~lib/api/types";

export const PENDING_AND_DRAFT = "PENDING,DRAFT";
export const DAY_CASE = "Day Case";
export const ADMISSION = "Admission";
export function getEffectiveRequestTypes(
  selectedRequestTypes: RequestType[],
  allowedRequestTypes: RequestType[],
): string[] {
  const baseRequestTypes =
    selectedRequestTypes.length > 0 ? selectedRequestTypes : allowedRequestTypes;

  let effectiveRequestTypes: string[] = [...baseRequestTypes];

  if (baseRequestTypes.includes(RequestType.OUTPATIENT)) {
    effectiveRequestTypes.push(RequestType.OTHER);
  }

  if (baseRequestTypes.includes(RequestType.INPATIENT)) {
    effectiveRequestTypes = effectiveRequestTypes.filter((type) => type !== RequestType.INPATIENT);
    effectiveRequestTypes.push(ADMISSION, DAY_CASE);
  }

  return effectiveRequestTypes;
}
