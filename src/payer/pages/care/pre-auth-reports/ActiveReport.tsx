import { ReportVisit } from "~lib/api/types";
import LoadingAnimation from "../../../components/animations/LoadingAnimation/LoadingAnimation";
import NoData from "../../../components/illustrations/NoData";
import EmptyState from "../../../components/ui/EmptyState";
import PrimaryPagination from "../../../components/ui/pagination/PrimaryPagination";
import TableDataItem from "../../../components/ui/table/TableDataItem";
import TableHeaderItem from "../../../components/ui/table/TableHeaderItem";
import { formatNumberToKes } from "../../../utils/formatCurrency";
import { defaultColumns, reportToExtraColumnsMap } from "./pre-auth-reports-constants";
import { Report, usePreAuthReports } from "./pre-auth-reports-context";
import ReportFilters from "./ReportFilters";

export type Column = {
  label: string;
  key: keyof ReportVisit;
};

export default function ActiveReport() {
  const {
    selectedFilters: { currentPage, currentSize },
    setSelectedFilters,
    isLoadingVisits,
    reportVisits,
    totalElements,
    totalPages,
    activeReport,
  } = usePreAuthReports();

  const extraColumns = reportToExtraColumnsMap.get(activeReport as Report) || [];

  const columns = [...defaultColumns, ...extraColumns];

  function getDataItem(visit: ReportVisit, key: keyof ReportVisit) {
    const value = visit[key];
    const isAmountKey = ["requestAmount", "approvedAmount"].includes(key);
    const shouldFormatNumber = isAmountKey && typeof value === "number";
    return shouldFormatNumber ? formatNumberToKes(value) : value;
  }

  return (
    <section className="relative grow">
      <ReportFilters />

      {isLoadingVisits ? (
        <div className=" mt-32 flex justify-center">
          <LoadingAnimation size={30} />
        </div>
      ) : reportVisits.length === 0 ? (
        <EmptyState
          illustration={<NoData size={250} />}
          message={{ title: "No reports found", description: "No reports available" }}
        />
      ) : (
        <>
          <table className="mt-8 w-full">
            <thead className="bg-gray-50">
              <tr>
                {columns.map((column) => (
                  <TableHeaderItem
                    className="px-1 text-sm"
                    key={column.label}
                    item={column.label}
                  />
                ))}
              </tr>
            </thead>
            <tbody>
              {reportVisits.map((visit) => (
                <tr key={visit.id}>
                  {columns.map((column) => (
                    <TableDataItem
                      className="px-1 text-sm"
                      key={column.key}
                      item={getDataItem(visit, column.key) || "-"}
                    />
                  ))}
                </tr>
              ))}
            </tbody>
          </table>

          <PrimaryPagination
            className="sticky top-[90%] mt-4"
            totalElements={Number(totalElements)}
            totalPages={Number(totalPages)}
            pageNumber={currentPage}
            onPageNumberClick={(page: number) => {
              setSelectedFilters((prev) => ({ ...prev, currentPage: page }));
            }}
            onSizeChange={(size: number) => {
              setSelectedFilters((prev) => ({ ...prev, currentSize: size }));
            }}
            pageSize={currentSize}
          />
        </>
      )}
    </section>
  );
}
