apiVersion: apps/v1
kind: Deployment
metadata:
  name: payer-portal-deployment
  namespace: dev-frontend
  labels:
    app: payer-portal
spec:
  replicas: 1
  selector:
    matchLabels:
      app: payer-portal
  template:
    metadata:
      labels:
        app: payer-portal
    spec:
      imagePullSecrets:
        - name: gcr-registry-key
      containers:
        - name: payer-portal
          image: {LATEST_IMAGE_DIGEST}
          ports:
            - containerPort: 3001
---
apiVersion: v1
kind: Service
metadata:
  name: payer-portal-loadbalancer-dev 
  namespace: dev-frontend
  annotations:
   metallb.io/address-pool: dev-public-ips
  labels:
    app: payer-portal-loadbalancer-dev
spec:
  type: LoadBalancer
  ports:
    - port: 3001
      targetPort: 80
      protocol: TCP
  selector:
    app: payer-portal
