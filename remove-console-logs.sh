#!/usr/bin/env bash
#
# remove-console-logs.sh
#

# Find target files excluding node_modules and dist
find . \
  \( -path "./node_modules" -o -path "./dist" \) -prune -false \
  -o \( -type f \( -name "*.js" -o -name "*.ts" -o -name "*.jsx" -o -name "*.tsx" \) \) -print \
| while IFS= read -r file; do
  # Remove lines containing console.log (in-place)
  sed -i.bak '/console\.log/d' "$file"
  # Optional: remove backup if everything's fine
  rm -f "${file}.bak"
done
