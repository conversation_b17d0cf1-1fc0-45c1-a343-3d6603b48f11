apiVersion: apps/v1
kind: Deployment
metadata:
  name: payer-portal-deployment
  namespace: frontend
  labels:
    app: payer-portal
spec:
  replicas: 1
  selector:
    matchLabels:
      app: payer-portal
  template:
    metadata:
      labels:
        app: payer-portal
    spec:
      containers:
        - name: payer-portal
          image: registry.digitalocean.com/lct-registry/payer-portal@sha256:****************************************************************
          ports:
            - containerPort: 3002

---
apiVersion: v1
kind: Service
metadata:
  name: payer-portal-service
  namespace: frontend
spec:
  selector:
    app: payer-portal
  ports:
    - port: 3002
      targetPort: 80
  type: ClusterIP
